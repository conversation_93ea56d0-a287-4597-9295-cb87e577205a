<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据差异页面功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-controls {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 0 10px;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .test-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.6;
            max-height: 500px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .test-status {
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
            font-weight: bold;
        }
        
        .test-status.success {
            color: #28a745;
        }
        
        .test-status.error {
            color: #dc3545;
        }
        
        .test-list {
            margin: 20px 0;
        }
        
        .test-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .test-item:hover {
            background: #e9ecef;
        }
        
        .loading {
            text-align: center;
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 数据差异页面功能测试</h1>
        
        <div class="test-controls">
            <button class="btn" onclick="runAllTests()">运行所有测试</button>
            <button class="btn" onclick="clearOutput()">清空输出</button>
            <button class="btn" onclick="showTestList()">显示测试列表</button>
        </div>
        
        <div class="test-status" id="testStatus">准备运行测试...</div>
        
        <div class="test-list" id="testList" style="display: none;">
            <h3>可用测试列表：</h3>
            <div id="testItems"></div>
        </div>
        
        <div class="test-output" id="testOutput">
            <div class="loading">等待测试开始...</div>
        </div>
    </div>

    <!-- 依赖的JavaScript文件 -->
    <script src="../jquery-2.1.4.min.js"></script>
    <script src="../json-formatter.js"></script>
    <script src="../table-enhancer.js"></script>
    <script src="../datadiff-enhanced.js"></script>
    <script src="datadiff-tests.js"></script>

    <script>
        // 重定向console输出到页面
        var originalLog = console.log;
        var originalError = console.error;
        var originalWarn = console.warn;
        var output = '';

        function appendOutput(message, type) {
            type = type || 'log';
            var timestamp = new Date().toLocaleTimeString();
            var prefix = type === 'error' ? '❌ ' : type === 'warn' ? '⚠️ ' : '📝 ';
            output += '[' + timestamp + '] ' + prefix + message + '\n';
            updateOutput();
        }

        console.log = function() {
            var message = Array.prototype.slice.call(arguments).join(' ');
            appendOutput(message, 'log');
            originalLog.apply(console, arguments);
        };

        console.error = function() {
            var message = Array.prototype.slice.call(arguments).join(' ');
            appendOutput(message, 'error');
            originalError.apply(console, arguments);
        };

        console.warn = function() {
            var message = Array.prototype.slice.call(arguments).join(' ');
            appendOutput(message, 'warn');
            originalWarn.apply(console, arguments);
        };

        function updateOutput() {
            document.getElementById('testOutput').textContent = output;
            // 自动滚动到底部
            var outputElement = document.getElementById('testOutput');
            outputElement.scrollTop = outputElement.scrollHeight;
        }

        function runAllTests() {
            clearOutput();
            document.getElementById('testStatus').textContent = '正在运行测试...';
            document.getElementById('testStatus').className = 'test-status';
            
            setTimeout(function() {
                try {
                    var success = window.DataDiffTests.run();
                    var statusElement = document.getElementById('testStatus');
                    
                    if (success) {
                        statusElement.textContent = '✅ 所有测试通过！';
                        statusElement.className = 'test-status success';
                    } else {
                        statusElement.textContent = '❌ 部分测试失败';
                        statusElement.className = 'test-status error';
                    }
                } catch (error) {
                    console.error('测试运行出错: ' + error.message);
                    document.getElementById('testStatus').textContent = '❌ 测试运行出错';
                    document.getElementById('testStatus').className = 'test-status error';
                }
            }, 100);
        }

        function clearOutput() {
            output = '';
            updateOutput();
        }

        function showTestList() {
            var testListElement = document.getElementById('testList');
            var testItemsElement = document.getElementById('testItems');
            
            if (testListElement.style.display === 'none') {
                try {
                    var tests = window.DataDiffTests.listTests();
                    testItemsElement.innerHTML = '';
                    
                    tests.forEach(function(testName) {
                        var item = document.createElement('div');
                        item.className = 'test-item';
                        item.textContent = testName;
                        item.onclick = function() {
                            runSingleTest(testName);
                        };
                        testItemsElement.appendChild(item);
                    });
                    
                    testListElement.style.display = 'block';
                } catch (error) {
                    console.error('获取测试列表失败: ' + error.message);
                }
            } else {
                testListElement.style.display = 'none';
            }
        }

        function runSingleTest(testName) {
            clearOutput();
            document.getElementById('testStatus').textContent = '正在运行测试: ' + testName;
            document.getElementById('testStatus').className = 'test-status';
            
            setTimeout(function() {
                try {
                    var success = window.DataDiffTests.runSingle(testName);
                    var statusElement = document.getElementById('testStatus');
                    
                    if (success) {
                        statusElement.textContent = '✅ 测试通过: ' + testName;
                        statusElement.className = 'test-status success';
                    } else {
                        statusElement.textContent = '❌ 测试失败: ' + testName;
                        statusElement.className = 'test-status error';
                    }
                } catch (error) {
                    console.error('测试运行出错: ' + error.message);
                    document.getElementById('testStatus').textContent = '❌ 测试运行出错';
                    document.getElementById('testStatus').className = 'test-status error';
                }
            }, 100);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('测试页面加载完成');
            console.log('可用的测试模块:');
            console.log('- JsonFormatter: ' + (typeof JsonFormatter !== 'undefined' ? '✅' : '❌'));
            console.log('- TableEnhancer: ' + (typeof TableEnhancer !== 'undefined' ? '✅' : '❌'));
            console.log('- DataDiffEnhancer: ' + (typeof DataDiffEnhancer !== 'undefined' ? '✅' : '❌'));
            console.log('- DataDiffTests: ' + (typeof DataDiffTests !== 'undefined' ? '✅' : '❌'));
            console.log('\n点击"运行所有测试"开始测试，或点击"显示测试列表"查看可用的单个测试。');
        });
    </script>
</body>
</html>
