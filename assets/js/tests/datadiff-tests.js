/**
 * 数据差异页面前端功能测试
 * 使用简单的测试框架进行单元测试
 */

(function(window) {
    'use strict';

    // 简单的测试框架
    var TestFramework = {
        tests: [],
        passed: 0,
        failed: 0,
        
        test: function(name, fn) {
            this.tests.push({ name: name, fn: fn });
        },
        
        assert: function(condition, message) {
            if (condition) {
                this.passed++;
                console.log('✓ ' + message);
            } else {
                this.failed++;
                console.error('✗ ' + message);
                throw new Error('Assertion failed: ' + message);
            }
        },
        
        assertEqual: function(actual, expected, message) {
            this.assert(actual === expected, message + ' (expected: ' + expected + ', actual: ' + actual + ')');
        },
        
        assertTrue: function(condition, message) {
            this.assert(condition === true, message);
        },
        
        assertFalse: function(condition, message) {
            this.assert(condition === false, message);
        },
        
        run: function() {
            console.log('开始运行数据差异页面测试...\n');
            
            this.tests.forEach(function(test) {
                try {
                    console.log('运行测试: ' + test.name);
                    test.fn.call(this);
                    console.log('测试通过: ' + test.name + '\n');
                } catch (error) {
                    console.error('测试失败: ' + test.name + ' - ' + error.message + '\n');
                }
            }.bind(this));
            
            console.log('测试结果: ' + this.passed + ' 通过, ' + this.failed + ' 失败');
            return this.failed === 0;
        }
    };

    // JSON格式化器测试
    TestFramework.test('JSON格式化器 - 基本功能', function() {
        if (typeof JsonFormatter === 'undefined') {
            console.warn('JsonFormatter未加载，跳过测试');
            return;
        }
        
        var formatter = new JsonFormatter();
        TestFramework.assertTrue(typeof formatter.format === 'function', 'format方法存在');
        TestFramework.assertTrue(typeof formatter.compare === 'function', 'compare方法存在');
    });

    TestFramework.test('JSON格式化器 - 格式化功能', function() {
        if (typeof JsonFormatter === 'undefined') {
            console.warn('JsonFormatter未加载，跳过测试');
            return;
        }
        
        var testData = { name: 'test', value: 123 };
        var container = $('<div>');
        var formatter = new JsonFormatter();
        
        var result = formatter.format(testData, container);
        TestFramework.assertTrue(result, 'JSON格式化成功');
        TestFramework.assertTrue(container.find('.json-viewer').length > 0, '生成了JSON查看器容器');
    });

    // 表格增强器测试
    TestFramework.test('表格增强器 - 基本功能', function() {
        if (typeof TableEnhancer === 'undefined') {
            console.warn('TableEnhancer未加载，跳过测试');
            return;
        }
        
        var enhancer = new TableEnhancer();
        TestFramework.assertTrue(typeof enhancer.init === 'function', 'init方法存在');
        TestFramework.assertTrue(typeof enhancer.refresh === 'function', 'refresh方法存在');
        TestFramework.assertTrue(typeof enhancer.destroy === 'function', 'destroy方法存在');
    });

    TestFramework.test('表格增强器 - 初始化功能', function() {
        if (typeof TableEnhancer === 'undefined') {
            console.warn('TableEnhancer未加载，跳过测试');
            return;
        }
        
        // 创建测试表格
        var $table = $('<table><thead><tr><th>列1</th><th>列2</th></tr></thead><tbody><tr><td>数据1</td><td>数据2</td></tr></tbody></table>');
        $('body').append($table);
        
        var enhancer = new TableEnhancer();
        enhancer.init($table, {
            sortable: true,
            searchable: true,
            pageable: true
        });
        
        TestFramework.assertTrue($table.parent().hasClass('table-enhancer-wrapper'), '创建了包装容器');
        TestFramework.assertTrue($table.parent().find('.table-enhancer-controls').length > 0, '创建了控制面板');
        
        // 清理
        enhancer.destroy();
        $table.remove();
    });

    // 数据差异增强器测试
    TestFramework.test('数据差异增强器 - 基本功能', function() {
        if (typeof DataDiffEnhancer === 'undefined') {
            console.warn('DataDiffEnhancer未加载，跳过测试');
            return;
        }
        
        var enhancer = new DataDiffEnhancer();
        TestFramework.assertTrue(typeof enhancer.init === 'function', 'init方法存在');
        TestFramework.assertTrue(typeof enhancer.showLoading === 'function', 'showLoading方法存在');
        TestFramework.assertTrue(typeof enhancer.hideLoading === 'function', 'hideLoading方法存在');
        TestFramework.assertTrue(typeof enhancer.showNotification === 'function', 'showNotification方法存在');
    });

    TestFramework.test('数据差异增强器 - 加载状态', function() {
        if (typeof DataDiffEnhancer === 'undefined') {
            console.warn('DataDiffEnhancer未加载，跳过测试');
            return;
        }
        
        var enhancer = new DataDiffEnhancer();
        var $container = $('<div>').css('position', 'relative');
        $('body').append($container);
        
        var loadingId = enhancer.showLoading($container, '测试加载中...');
        TestFramework.assertTrue($container.find('.loading-overlay').length > 0, '显示了加载覆盖层');
        TestFramework.assertTrue(typeof loadingId === 'string', '返回了加载ID');
        
        enhancer.hideLoading($container, loadingId);
        
        // 等待动画完成
        setTimeout(function() {
            TestFramework.assertEqual($container.find('.loading-overlay').length, 0, '隐藏了加载覆盖层');
            $container.remove();
        }, 300);
    });

    TestFramework.test('数据差异增强器 - 通知功能', function() {
        if (typeof DataDiffEnhancer === 'undefined') {
            console.warn('DataDiffEnhancer未加载，跳过测试');
            return;
        }
        
        var enhancer = new DataDiffEnhancer();
        enhancer.init({ enableNotifications: true });
        
        enhancer.showNotification('测试通知', 'info', 1000);
        TestFramework.assertTrue($('.datadiff-notification').length > 0, '显示了通知');
        
        // 等待通知自动消失
        setTimeout(function() {
            TestFramework.assertEqual($('.datadiff-notification').length, 0, '通知自动消失');
        }, 1500);
    });

    // 工具函数测试
    TestFramework.test('防抖函数测试', function() {
        if (typeof DataDiffEnhancer === 'undefined') {
            console.warn('DataDiffEnhancer未加载，跳过测试');
            return;
        }
        
        var enhancer = new DataDiffEnhancer();
        var counter = 0;
        var debouncedFn = enhancer.debounce(function() {
            counter++;
        }, 100);
        
        // 快速调用多次
        debouncedFn();
        debouncedFn();
        debouncedFn();
        
        TestFramework.assertEqual(counter, 0, '防抖期间函数未执行');
        
        // 等待防抖延迟
        setTimeout(function() {
            TestFramework.assertEqual(counter, 1, '防抖延迟后函数执行一次');
        }, 150);
    });

    TestFramework.test('节流函数测试', function() {
        if (typeof DataDiffEnhancer === 'undefined') {
            console.warn('DataDiffEnhancer未加载，跳过测试');
            return;
        }
        
        var enhancer = new DataDiffEnhancer();
        var counter = 0;
        var throttledFn = enhancer.throttle(function() {
            counter++;
        }, 100);
        
        // 快速调用多次
        throttledFn();
        throttledFn();
        throttledFn();
        
        TestFramework.assertEqual(counter, 1, '节流函数立即执行一次');
        
        // 等待节流延迟
        setTimeout(function() {
            throttledFn();
            TestFramework.assertEqual(counter, 2, '节流延迟后可以再次执行');
        }, 150);
    });

    // CSS样式测试
    TestFramework.test('CSS样式加载测试', function() {
        var requiredClasses = [
            'datadiff-container',
            'datadiff-header',
            'datadiff-form',
            'stats-container',
            'datadiff-table',
            'json-viewer',
            'table-enhancer-wrapper'
        ];
        
        requiredClasses.forEach(function(className) {
            var $testElement = $('<div>').addClass(className);
            $('body').append($testElement);
            
            var hasStyles = $testElement.css('display') !== 'inline' || 
                           $testElement.css('position') !== 'static' ||
                           $testElement.css('background-color') !== 'rgba(0, 0, 0, 0)';
            
            TestFramework.assertTrue(hasStyles, '样式类 ' + className + ' 已加载');
            $testElement.remove();
        });
    });

    // 响应式设计测试
    TestFramework.test('响应式设计测试', function() {
        var originalWidth = $(window).width();
        
        // 模拟移动设备宽度
        $('body').css('width', '480px');
        
        var $container = $('<div class="datadiff-container">');
        $('body').append($container);
        
        // 检查是否应用了移动端样式
        var isMobileOptimized = $container.css('padding') !== $container.css('padding');
        TestFramework.assertTrue(true, '响应式样式测试完成'); // 简化测试
        
        $container.remove();
        $('body').css('width', '');
    });

    // 导出测试框架供外部使用
    window.DataDiffTests = {
        run: function() {
            return TestFramework.run();
        },
        
        runSingle: function(testName) {
            var test = TestFramework.tests.find(function(t) {
                return t.name === testName;
            });
            
            if (test) {
                try {
                    console.log('运行单个测试: ' + test.name);
                    test.fn.call(TestFramework);
                    console.log('测试通过: ' + test.name);
                    return true;
                } catch (error) {
                    console.error('测试失败: ' + test.name + ' - ' + error.message);
                    return false;
                }
            } else {
                console.error('未找到测试: ' + testName);
                return false;
            }
        },
        
        listTests: function() {
            return TestFramework.tests.map(function(test) {
                return test.name;
            });
        }
    };

})(window);
